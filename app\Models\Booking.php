<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class Booking extends Model
{
    protected $fillable = ['event_id', 'name', 'email', 'phone', 'date', 'time', 'selected_location', 'custom_fields', 'custom_fields_value', 'status'];

    protected $casts = [
        'custom_fields' => 'array',
        'custom_fields_value' => 'array',
        'selected_location' => 'array'
    ];

    // Relationship to CalendarEvent
    public function event()
    {
        return $this->belongsTo(CalendarEvent::class, 'event_id');
    }

    // Alternative relationship name for clarity
    public function calendarEvent()
    {
        return $this->belongsTo(CalendarEvent::class, 'event_id');
    }

    /**
     * Get status display information
     */
    public function getStatusDisplayAttribute()
    {
        $statusMap = [
            'scheduled' => ['name' => 'Scheduled', 'icon' => 'ti-calendar-check', 'class' => 'text-primary'],
            'reschedule' => ['name' => 'Reschedule', 'icon' => 'ti-calendar-time', 'class' => 'text-warning'],
            'show_up' => ['name' => 'Show Up', 'icon' => 'ti-check-circle', 'class' => 'text-success'],
            'no_show' => ['name' => 'No Show', 'icon' => 'ti-x-circle', 'class' => 'text-danger'],
            'cancel' => ['name' => 'Cancel', 'icon' => 'ti-ban', 'class' => 'text-secondary']
        ];

        return $statusMap[$this->status] ?? $statusMap['scheduled'];
    }

    // Helper method to safely get custom fields as array
    public function getCustomFieldsArrayAttribute()
    {
        $customFields = $this->custom_fields;

        if (is_string($customFields)) {
            $customFields = json_decode($customFields, true);
        }

        return is_array($customFields) ? $customFields : [];
    }

    // Helper method to get custom fields count
    public function getCustomFieldsCountAttribute()
    {
        return count($this->custom_fields_array);
    }
}
