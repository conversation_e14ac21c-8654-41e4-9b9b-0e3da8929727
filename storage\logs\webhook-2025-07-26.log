[2025-07-26 06:14:11] local.INFO: Starting webhook dispatch for action: booking.event_created {"timestamp":"2025-07-26T06:14:11.325762Z","source":"crm_webhook_system","action":"booking.event_created","user_id":84,"entity_type":null,"entity_id":null,"status":"dispatching"} 
[2025-07-26 06:14:15] local.ERROR: Webhook failed for OMX FLOW: cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2033 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook {"timestamp":"2025-07-26T06:14:15.134304Z","source":"crm_webhook_system","action":"booking.event_created","module_name":"OMX FLOW","webhook_url":"http://127.0.0.1:2000/external-crm/webhook","status":"failed","status_code":null,"response_time_ms":3759.0,"user_id":84,"entity_id":4,"entity_type":null,"request_payload":{"action":"booking.event_created","timestamp":"2025-07-26T06:14:11.375214Z","data":{"id":4,"title":"AI Bot Live","start_date":"2025-07-26 06:13:57","end_date":"2026-07-26 06:13:57","duration":60,"booking_per_slot":1,"minimum_notice":60,"description":"This is the best live AI agent.","location":"in_person","meet_link":"8617555736","physical_address":"https://maps.app.goo.gl/XvDSMjZrKaR9ifpy5","custom_fields":null,"date_override":null,"created_by":84,"slots_created":2928,"triggered_by":{"user_id":84,"email":"<EMAIL>","name":"parichay  Bot flow","type":"company"}},"user_id":84,"source":{"system":"krishna","version":"1.0","url":"http://localhost:8000"}},"error_message":"cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2033 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook","response_body":null} 
[2025-07-26 06:14:15] local.WARNING: Webhook dispatch completed for action: booking.event_created. Success: 0, Failed: 1 {"timestamp":"2025-07-26T06:14:15.135679Z","source":"crm_webhook_system","action":"booking.event_created","user_id":84,"status":"completed","total_modules":1,"successful_modules":0,"failed_modules":1,"modules":["OMX FLOW"],"results":{"OMX FLOW":{"success":false,"error":"cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2033 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook","integration":"OMX FLOW"}}} 
[2025-07-26 06:44:56] local.INFO: Starting webhook dispatch for action: booking.booking_form_submitted {"timestamp":"2025-07-26T06:44:56.741078Z","source":"crm_webhook_system","action":"booking.booking_form_submitted","user_id":84,"entity_type":"Booking","entity_id":5,"status":"dispatching"} 
[2025-07-26 06:45:02] local.ERROR: Webhook failed for OMX FLOW: cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2057 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook {"timestamp":"2025-07-26T06:45:02.013624Z","source":"crm_webhook_system","action":"booking.booking_form_submitted","module_name":"OMX FLOW","webhook_url":"http://127.0.0.1:2000/external-crm/webhook","status":"failed","status_code":null,"response_time_ms":5197.0,"user_id":84,"entity_id":5,"entity_type":null,"request_payload":{"action":"booking.booking_form_submitted","timestamp":"2025-07-26T06:44:56.817070Z","data":{"event_id":4,"name":"Parichay Singha Parichay Singha","email":"<EMAIL>","phone":"08617555736","date":"2025-07-30","time":"14:30","selected_location":{"type":"in_person","value":"https://maps.app.goo.gl/XvDSMjZrKaR9ifpy5","display":"In-person meeting"},"custom_fields":[],"custom_fields_value":[],"updated_at":"2025-07-26T06:44:56.000000Z","created_at":"2025-07-26T06:44:56.000000Z","id":5,"form_data":{"name":"Parichay Singha Parichay Singha","email":"<EMAIL>","phone":"08617555736","date":"2025-07-30","time":"14:30","selected_location":{"type":"in_person","value":"https://maps.app.goo.gl/XvDSMjZrKaR9ifpy5","display":"In-person meeting"},"custom_fields":[],"custom_fields_value":[],"booking_type":"public"},"triggered_by":{"user_id":84,"email":"<EMAIL>","name":"parichay  Bot flow","type":"company"}},"user_id":84,"source":{"system":"krishna","version":"1.0","url":"http://localhost:8000"}},"error_message":"cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2057 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook","response_body":null} 
[2025-07-26 06:45:02] local.WARNING: Webhook dispatch completed for action: booking.booking_form_submitted. Success: 0, Failed: 1 {"timestamp":"2025-07-26T06:45:02.014903Z","source":"crm_webhook_system","action":"booking.booking_form_submitted","user_id":84,"status":"completed","total_modules":1,"successful_modules":0,"failed_modules":1,"modules":["OMX FLOW"],"results":{"OMX FLOW":{"success":false,"error":"cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2057 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook","integration":"OMX FLOW"}}} 
[2025-07-26 07:04:02] local.INFO: Starting webhook dispatch for action: crm.lead_created {"timestamp":"2025-07-26T07:04:02.258592Z","source":"crm_webhook_system","action":"crm.lead_created","user_id":84,"entity_type":"Lead","entity_id":12,"status":"dispatching"} 
[2025-07-26 07:04:04] local.ERROR: Webhook failed for OMX FLOW: cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2012 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook {"timestamp":"2025-07-26T07:04:04.331664Z","source":"crm_webhook_system","action":"crm.lead_created","module_name":"OMX FLOW","webhook_url":"http://127.0.0.1:2000/external-crm/webhook","status":"failed","status_code":null,"response_time_ms":2069.0,"user_id":84,"entity_id":12,"entity_type":"Lead","request_payload":{"action":"crm.lead_created","timestamp":"2025-07-26T07:04:02.262801Z","data":{"name":"Parichay Singha","email":"<EMAIL>","phone":"+9186956321505","subject":"Booking","user_id":"85","pipeline_id":27,"stage_id":105,"created_by":84,"date":"2025-07-26","next_follow_up_date":null,"updated_at":"2025-07-26T07:04:02.000000Z","created_at":"2025-07-26T07:04:02.000000Z","id":12,"stage":{"id":105,"name":"New","pipeline_id":27,"created_by":84,"order":0,"created_at":"2025-07-26T07:03:22.000000Z","updated_at":"2025-07-26T07:03:22.000000Z"},"pipeline":{"id":27,"name":"Default Pipeline","created_by":84,"created_at":"2025-07-26T07:03:22.000000Z","updated_at":"2025-07-26T07:03:22.000000Z"},"triggered_by":{"user_id":84,"email":"<EMAIL>","name":"parichay  Bot flow","type":"company"}},"user_id":84,"source":{"system":"krishna","version":"1.0","url":"http://localhost:8000"}},"error_message":"cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2012 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook","response_body":null} 
[2025-07-26 07:04:04] local.WARNING: Webhook dispatch completed for action: crm.lead_created. Success: 0, Failed: 1 {"timestamp":"2025-07-26T07:04:04.332105Z","source":"crm_webhook_system","action":"crm.lead_created","user_id":84,"status":"completed","total_modules":1,"successful_modules":0,"failed_modules":1,"modules":["OMX FLOW"],"results":{"OMX FLOW":{"success":false,"error":"cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2012 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook","integration":"OMX FLOW"}}} 
[2025-07-26 07:04:59] local.INFO: Starting webhook dispatch for action: crm.lead_stage_changed {"timestamp":"2025-07-26T07:04:59.152296Z","source":"crm_webhook_system","action":"crm.lead_stage_changed","user_id":84,"entity_type":"Lead","entity_id":12,"status":"dispatching"} 
[2025-07-26 07:05:01] local.ERROR: Webhook failed for OMX FLOW: cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2013 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook {"timestamp":"2025-07-26T07:05:01.211275Z","source":"crm_webhook_system","action":"crm.lead_stage_changed","module_name":"OMX FLOW","webhook_url":"http://127.0.0.1:2000/external-crm/webhook","status":"failed","status_code":null,"response_time_ms":2048.0,"user_id":84,"entity_id":12,"entity_type":"Lead","request_payload":{"action":"crm.lead_stage_changed","timestamp":"2025-07-26T07:04:59.162911Z","data":{"id":12,"name":"Parichay Singha","email":"<EMAIL>","phone":"+9186956321505","subject":"Booking","user_id":85,"pipeline_id":27,"stage_id":105,"sources":[],"products":[],"notes":null,"labels":[],"order":0,"created_by":84,"is_active":1,"is_converted":0,"date":"2025-07-26","next_follow_up_date":null,"created_at":"2025-07-26T07:04:02.000000Z","updated_at":"2025-07-26T07:04:51.000000Z","stage":{"id":105,"name":"New","pipeline_id":27,"created_by":84,"order":0,"created_at":"2025-07-26T07:03:22.000000Z","updated_at":"2025-07-26T07:03:22.000000Z"},"users":[{"id":84,"name":"parichay  Bot flow","email":"<EMAIL>","email_verified_at":"2025-07-21T06:08:10.000000Z","plan":10,"plan_expire_date":"2026-07-21","requested_plan":0,"trial_plan":0,"trial_expire_date":null,"type":"company","system_admin_company_id":null,"company_name":null,"company_description":null,"module_permissions":{"crm":["create lead","create deal","create form builder","create contract","create pipeline","create stage","create source","create label","view crm dashboard","view lead","view deal","view form builder","view contract","view pipeline","view stage","view source","view label","delete lead","delete deal","delete form builder","delete contract","delete pipeline","delete stage","delete source","delete label","manage lead","edit lead","manage deal","edit deal","manage form builder","edit form builder","manage contract","edit contract","manage pipeline","edit pipeline","manage stage","edit stage","manage source","edit source","manage label","edit label"],"hrm":["create employee","create set salary","create pay slip","create leave","create attendance","create training","create award","create branch","create department","create designation","create document type","view hrm dashboard","view employee","view set salary","view pay slip","view leave","view attendance","view training","view award","view branch","view department","view designation","view document type","delete employee","delete set salary","delete pay slip","delete leave","delete attendance","delete training","delete award","delete branch","delete department","delete designation","delete document type","manage employee","edit employee","manage set salary","edit set salary","manage pay slip","edit pay slip","manage leave","edit leave","manage attendance","edit attendance","manage training","edit training","manage award","edit award","manage branch","edit branch","manage department","edit department","manage designation","edit designation","manage document type","edit document type"],"account":["create customer","create vender","create invoice","create bill","create revenue","create payment","create proposal","create goal","create credit note","create debit note","create bank account","create bank transfer","create transaction","create chart of account","create journal entry","create assets","create constant custom field","view account dashboard","view customer","view vender","view invoice","view bill","view revenue","view payment","view proposal","view goal","view credit note","view debit note","view bank account","view bank transfer","view transaction","view chart of account","view journal entry","view assets","view constant custom field","view report","delete customer","delete vender","delete invoice","delete bill","delete revenue","delete payment","delete proposal","delete goal","delete credit note","delete debit note","delete bank account","delete bank transfer","delete transaction","delete chart of account","delete journal entry","delete assets","delete constant custom field","manage customer","edit customer","manage vender","edit vender","manage invoice","edit invoice","manage bill","edit bill","manage revenue","edit revenue","manage payment","edit payment","manage proposal","edit proposal","manage goal","edit goal","manage credit note","edit credit note","manage debit note","edit debit note","manage bank account","edit bank account","manage bank transfer","edit bank transfer","manage transaction","edit transaction","manage chart of account","edit chart of account","manage journal entry","edit journal entry","manage assets","edit assets","manage constant custom field","edit constant custom field","manage report"],"project":["create project","create project task","create timesheet","create bug report","create milestone","create project stage","create project task stage","create project expense","create activity","create bug status","view project dashboard","view project","view project task","view timesheet","view bug report","view milestone","view project stage","view project task stage","view project expense","view activity","view bug status","delete project","delete project task","delete timesheet","delete bug report","delete milestone","delete project stage","delete project task stage","delete project expense","delete activity","delete bug status","manage project","edit project","manage project task","edit project task","manage timesheet","edit timesheet","manage bug report","edit bug report","manage milestone","edit milestone","manage project stage","edit project stage","manage project task stage","edit project task stage","manage project expense","edit project expense","manage activity","edit activity","manage bug status","edit bug status"],"pos":["create warehouse","create purchase","create quotation","create pos","create barcode","create product","create product category","create product unit","view pos dashboard","view warehouse","view purchase","view quotation","view pos","view product","view product category","view product unit","delete warehouse","delete purchase","delete quotation","delete pos","delete product","delete product category","delete product unit","manage warehouse","edit warehouse","manage purchase","edit purchase","manage quotation","edit quotation","manage pos","edit pos","manage product","edit product","manage product category","edit product category","manage product unit","edit product unit"],"support":["create support","view support dashboard","view support","delete support","manage support","edit support","reply support"],"user_management":["create user","create client","view user","view client","delete user","delete client","manage user","edit user","manage client","edit client"],"booking":["create booking","create appointment","create appointment booking","create calendar event","view booking dashboard","view booking","show booking","view appointment","show appointment","view appointment booking","show appointment booking","view calendar event","show calendar event","delete booking","delete appointment","delete appointment booking","delete calendar event","manage booking","edit booking","manage appointment","edit appointment","manage appointment booking","edit appointment booking","manage calendar event","edit calendar event"],"omx_flow":["access omx flow","whatsapp_flows","whatsapp_orders","campaigns","templates","chatbot"],"personal_tasks":["create personal task","create personal task comment","create personal task file","create personal task checklist","view personal task","delete personal task","delete personal task comment","delete personal task file","delete personal task checklist","manage personal task","edit personal task","edit personal task comment","edit personal task checklist","manage personal task time tracking"],"automatish":["access automatish"]},"storage_limit":0.0,"avatar":"","messenger_color":"#2180f3","lang":"en","default_pipeline":null,"active_status":0,"delete_status":1,"mode":"light","dark_mode":0,"is_disable":1,"is_enable_login":1,"is_active":1,"referral_code":0,"used_referral_code":0,"commission_amount":0,"last_login_at":null,"created_by":7,"created_at":"2025-07-21T06:08:10.000000Z","updated_at":"2025-07-25T19:07:41.000000Z","is_email_verified":0,"profile":"http://localhost:8000/storage/avatar.png","pivot":{"lead_id":12,"user_id":84}},{"id":85,"name":"Parichay Singha","email":"<EMAIL>","email_verified_at":"2025-07-25T19:09:52.000000Z","plan":null,"plan_expire_date":null,"requested_plan":0,"trial_plan":0,"trial_expire_date":null,"type":"employee","system_admin_company_id":null,"company_name":null,"company_description":null,"module_permissions":{"crm":["create lead","create deal","create form builder","create contract","create pipeline","create stage","create source","create label","view crm dashboard","view lead","view deal","view form builder","view contract","view pipeline","view stage","view source","view label","delete lead","delete deal","delete form builder","delete contract","delete pipeline","delete stage","delete source","delete label","manage lead","edit lead","manage deal","edit deal","manage form builder","edit form builder","manage contract","edit contract","manage pipeline","edit pipeline","manage stage","edit stage","manage source","edit source","manage label","edit label"]},"storage_limit":0.0,"avatar":"avatar.png","messenger_color":"#2180f3","lang":"en","default_pipeline":null,"active_status":0,"delete_status":1,"mode":"light","dark_mode":0,"is_disable":1,"is_enable_login":1,"is_active":1,"referral_code":0,"used_referral_code":0,"commission_amount":0,"last_login_at":null,"created_by":84,"created_at":"2025-07-25T19:09:52.000000Z","updated_at":"2025-07-25T19:09:52.000000Z","is_email_verified":0,"profile":"http://localhost:8000/storage/avatar.png","pivot":{"lead_id":12,"user_id":85}}],"pipeline":{"id":27,"name":"Default Pipeline","created_by":84,"created_at":"2025-07-26T07:03:22.000000Z","updated_at":"2025-07-26T07:03:22.000000Z"},"old_stage_id":105,"new_stage_id":"106","triggered_by":{"user_id":84,"email":"<EMAIL>","name":"parichay  Bot flow","type":"company"}},"user_id":84,"source":{"system":"krishna","version":"1.0","url":"http://localhost:8000"}},"error_message":"cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2013 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook","response_body":null} 
[2025-07-26 07:05:01] local.WARNING: Webhook dispatch completed for action: crm.lead_stage_changed. Success: 0, Failed: 1 {"timestamp":"2025-07-26T07:05:01.213153Z","source":"crm_webhook_system","action":"crm.lead_stage_changed","user_id":84,"status":"completed","total_modules":1,"successful_modules":0,"failed_modules":1,"modules":["OMX FLOW"],"results":{"OMX FLOW":{"success":false,"error":"cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2013 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook","integration":"OMX FLOW"}}} 
[2025-07-26 07:33:44] local.INFO: Starting webhook dispatch for action: booking.booking_form_submitted {"timestamp":"2025-07-26T07:33:44.082246Z","source":"crm_webhook_system","action":"booking.booking_form_submitted","user_id":84,"entity_type":"Booking","entity_id":6,"status":"dispatching"} 
[2025-07-26 07:33:46] local.ERROR: Webhook failed for OMX FLOW: cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2046 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook {"timestamp":"2025-07-26T07:33:46.294294Z","source":"crm_webhook_system","action":"booking.booking_form_submitted","module_name":"OMX FLOW","webhook_url":"http://127.0.0.1:2000/external-crm/webhook","status":"failed","status_code":null,"response_time_ms":2184.0,"user_id":84,"entity_id":6,"entity_type":null,"request_payload":{"action":"booking.booking_form_submitted","timestamp":"2025-07-26T07:33:44.110048Z","data":{"event_id":4,"name":"Parichay Singha Parichay Singha","email":"<EMAIL>","phone":"08617555736","date":"2025-07-29","time":"14:00","selected_location":{"type":"in_person","value":"https://maps.app.goo.gl/XvDSMjZrKaR9ifpy5","display":"In-person meeting"},"custom_fields":[],"custom_fields_value":[],"updated_at":"2025-07-26T07:33:43.000000Z","created_at":"2025-07-26T07:33:43.000000Z","id":6,"form_data":{"name":"Parichay Singha Parichay Singha","email":"<EMAIL>","phone":"08617555736","date":"2025-07-29","time":"14:00","selected_location":{"type":"in_person","value":"https://maps.app.goo.gl/XvDSMjZrKaR9ifpy5","display":"In-person meeting"},"custom_fields":[],"custom_fields_value":[],"booking_type":"public"},"triggered_by":{"user_id":84,"email":"<EMAIL>","name":"parichay  Bot flow","type":"company"}},"user_id":84,"source":{"system":"krishna","version":"1.0","url":"http://localhost:8000"}},"error_message":"cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2046 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook","response_body":null} 
[2025-07-26 07:33:46] local.WARNING: Webhook dispatch completed for action: booking.booking_form_submitted. Success: 0, Failed: 1 {"timestamp":"2025-07-26T07:33:46.296092Z","source":"crm_webhook_system","action":"booking.booking_form_submitted","user_id":84,"status":"completed","total_modules":1,"successful_modules":0,"failed_modules":1,"modules":["OMX FLOW"],"results":{"OMX FLOW":{"success":false,"error":"cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2046 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook","integration":"OMX FLOW"}}} 
[2025-07-26 07:34:06] local.INFO: Starting webhook dispatch for action: booking.booking_form_submitted {"timestamp":"2025-07-26T07:34:06.109413Z","source":"crm_webhook_system","action":"booking.booking_form_submitted","user_id":84,"entity_type":"Booking","entity_id":7,"status":"dispatching"} 
[2025-07-26 07:34:08] local.ERROR: Webhook failed for OMX FLOW: cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2034 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook {"timestamp":"2025-07-26T07:34:08.256367Z","source":"crm_webhook_system","action":"booking.booking_form_submitted","module_name":"OMX FLOW","webhook_url":"http://127.0.0.1:2000/external-crm/webhook","status":"failed","status_code":null,"response_time_ms":2113.0,"user_id":84,"entity_id":7,"entity_type":null,"request_payload":{"action":"booking.booking_form_submitted","timestamp":"2025-07-26T07:34:06.143054Z","data":{"event_id":4,"name":"Parichay Singha Parichay Singha","email":"<EMAIL>","phone":"08617555736","date":"2025-07-30","time":"11:30","selected_location":{"type":"in_person","value":"https://maps.app.goo.gl/XvDSMjZrKaR9ifpy5","display":"In-person meeting"},"custom_fields":[],"custom_fields_value":[],"updated_at":"2025-07-26T07:34:06.000000Z","created_at":"2025-07-26T07:34:06.000000Z","id":7,"form_data":{"name":"Parichay Singha Parichay Singha","email":"<EMAIL>","phone":"08617555736","date":"2025-07-30","time":"11:30","selected_location":{"type":"in_person","value":"https://maps.app.goo.gl/XvDSMjZrKaR9ifpy5","display":"In-person meeting"},"custom_fields":[],"custom_fields_value":[],"booking_type":"public"},"triggered_by":{"user_id":84,"email":"<EMAIL>","name":"parichay  Bot flow","type":"company"}},"user_id":84,"source":{"system":"krishna","version":"1.0","url":"http://localhost:8000"}},"error_message":"cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2034 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook","response_body":null} 
[2025-07-26 07:34:08] local.WARNING: Webhook dispatch completed for action: booking.booking_form_submitted. Success: 0, Failed: 1 {"timestamp":"2025-07-26T07:34:08.258622Z","source":"crm_webhook_system","action":"booking.booking_form_submitted","user_id":84,"status":"completed","total_modules":1,"successful_modules":0,"failed_modules":1,"modules":["OMX FLOW"],"results":{"OMX FLOW":{"success":false,"error":"cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2034 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook","integration":"OMX FLOW"}}} 
[2025-07-26 07:35:00] local.INFO: Starting webhook dispatch for action: booking.booking_form_submitted {"timestamp":"2025-07-26T07:35:00.636000Z","source":"crm_webhook_system","action":"booking.booking_form_submitted","user_id":84,"entity_type":"Booking","entity_id":8,"status":"dispatching"} 
[2025-07-26 07:35:02] local.ERROR: Webhook failed for OMX FLOW: cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2026 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook {"timestamp":"2025-07-26T07:35:02.786785Z","source":"crm_webhook_system","action":"booking.booking_form_submitted","module_name":"OMX FLOW","webhook_url":"http://127.0.0.1:2000/external-crm/webhook","status":"failed","status_code":null,"response_time_ms":2117.0,"user_id":84,"entity_id":8,"entity_type":null,"request_payload":{"action":"booking.booking_form_submitted","timestamp":"2025-07-26T07:35:00.669693Z","data":{"event_id":4,"name":"Parichay Singha Parichay Singha","email":"<EMAIL>","phone":"08617555736","date":"2025-08-14","time":"13:30","selected_location":{"type":"zoom","value":"https://us05web.zoom.us/j/83543439265?pwd=ZnxZPBrm690RQaiajOItNxTQtaprom.1","display":"Zoom"},"custom_fields":[],"custom_fields_value":[],"updated_at":"2025-07-26T07:35:00.000000Z","created_at":"2025-07-26T07:35:00.000000Z","id":8,"form_data":{"name":"Parichay Singha Parichay Singha","email":"<EMAIL>","phone":"08617555736","date":"2025-08-14","time":"13:30","selected_location":{"type":"zoom","value":"https://us05web.zoom.us/j/83543439265?pwd=ZnxZPBrm690RQaiajOItNxTQtaprom.1","display":"Zoom"},"custom_fields":[],"custom_fields_value":[],"booking_type":"public"},"triggered_by":{"user_id":84,"email":"<EMAIL>","name":"parichay  Bot flow","type":"company"}},"user_id":84,"source":{"system":"krishna","version":"1.0","url":"http://localhost:8000"}},"error_message":"cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2026 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook","response_body":null} 
[2025-07-26 07:35:02] local.WARNING: Webhook dispatch completed for action: booking.booking_form_submitted. Success: 0, Failed: 1 {"timestamp":"2025-07-26T07:35:02.788001Z","source":"crm_webhook_system","action":"booking.booking_form_submitted","user_id":84,"status":"completed","total_modules":1,"successful_modules":0,"failed_modules":1,"modules":["OMX FLOW"],"results":{"OMX FLOW":{"success":false,"error":"cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2026 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook","integration":"OMX FLOW"}}} 
[2025-07-26 07:36:03] local.INFO: Starting webhook dispatch for action: booking.booking_form_submitted {"timestamp":"2025-07-26T07:36:03.453213Z","source":"crm_webhook_system","action":"booking.booking_form_submitted","user_id":84,"entity_type":"Booking","entity_id":9,"status":"dispatching"} 
[2025-07-26 07:36:05] local.ERROR: Webhook failed for OMX FLOW: cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2047 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook {"timestamp":"2025-07-26T07:36:05.623602Z","source":"crm_webhook_system","action":"booking.booking_form_submitted","module_name":"OMX FLOW","webhook_url":"http://127.0.0.1:2000/external-crm/webhook","status":"failed","status_code":null,"response_time_ms":2147.0,"user_id":84,"entity_id":9,"entity_type":null,"request_payload":{"action":"booking.booking_form_submitted","timestamp":"2025-07-26T07:36:03.476583Z","data":{"event_id":4,"name":"Jatndea Nath Singha","email":"<EMAIL>","phone":"9932313212","date":"2025-07-28","time":"13:30","selected_location":{"type":"zoom","value":"https://us05web.zoom.us/j/83543439265?pwd=ZnxZPBrm690RQaiajOItNxTQtaprom.1","display":"Zoom"},"custom_fields":[],"custom_fields_value":[],"updated_at":"2025-07-26T07:36:03.000000Z","created_at":"2025-07-26T07:36:03.000000Z","id":9,"form_data":{"name":"Jatndea Nath Singha","email":"<EMAIL>","phone":"9932313212","date":"2025-07-28","time":"13:30","selected_location":{"type":"zoom","value":"https://us05web.zoom.us/j/83543439265?pwd=ZnxZPBrm690RQaiajOItNxTQtaprom.1","display":"Zoom"},"custom_fields":[],"custom_fields_value":[],"booking_type":"public"},"triggered_by":{"user_id":84,"email":"<EMAIL>","name":"parichay  Bot flow","type":"company"}},"user_id":84,"source":{"system":"krishna","version":"1.0","url":"http://localhost:8000"}},"error_message":"cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2047 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook","response_body":null} 
[2025-07-26 07:36:05] local.WARNING: Webhook dispatch completed for action: booking.booking_form_submitted. Success: 0, Failed: 1 {"timestamp":"2025-07-26T07:36:05.625061Z","source":"crm_webhook_system","action":"booking.booking_form_submitted","user_id":84,"status":"completed","total_modules":1,"successful_modules":0,"failed_modules":1,"modules":["OMX FLOW"],"results":{"OMX FLOW":{"success":false,"error":"cURL error 7: Failed to connect to 127.0.0.1 port 2000 after 2047 ms: Couldn't connect to server (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for http://127.0.0.1:2000/external-crm/webhook","integration":"OMX FLOW"}}} 
